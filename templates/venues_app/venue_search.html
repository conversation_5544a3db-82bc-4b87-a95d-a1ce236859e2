{% extends 'base.html' %}
{% load i18n %}
{% load review_tags %}

{% block title %}{% trans "Search Venues" %} - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Venue Search */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-50: #fafafa;
        --cw-neutral-100: #f5f5f5;
        --cw-neutral-200: #e5e5e5;
        --cw-neutral-300: #d4d4d4;
        --cw-neutral-400: #a3a3a3;
        --cw-neutral-500: #737373;
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;
        --cw-neutral-900: #171717;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        --cw-gradient-card: linear-gradient(135deg, #ffffff 0%, var(--cw-brand-accent) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
    }

    /* Main Section */
    .search-section {
        background: var(--cw-gradient-hero);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .search-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    /* Page Header */
    .page-header {
        text-align: center;
        margin-bottom: 3rem;
    }

    .page-title {
        font-family: var(--cw-font-display);
        font-size: 3rem;
        font-weight: 700;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .page-subtitle {
        font-size: 1.25rem;
        color: var(--cw-neutral-600);
        margin-bottom: 0;
        line-height: 1.6;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Search Filters Card */
    .search-filters {
        background: white;
        border: 1px solid var(--cw-neutral-200);
        border-radius: 1rem;
        padding: 2.5rem;
        margin-bottom: 3rem;
        box-shadow: var(--cw-shadow-md);
    }

    .filter-section {
        margin-bottom: 2rem;
    }

    .filter-section:last-child {
        margin-bottom: 0;
    }

    .filter-label {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        font-size: 1.125rem;
        display: block;
    }

    /* Form Controls */
    .form-control-cw {
        border: 2px solid var(--cw-neutral-200);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        font-family: var(--cw-font-primary);
        background: white;
    }

    .form-control-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    .form-select-cw {
        border: 2px solid var(--cw-neutral-200);
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.2s ease;
        font-family: var(--cw-font-primary);
        background: white;
    }

    .form-select-cw:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 0.2rem rgba(47, 22, 15, 0.1);
        outline: none;
    }

    /* Custom Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
        font-family: var(--cw-font-heading);
    }

    .btn-cw-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(47, 22, 15, 0.3);
        color: white;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
        font-family: var(--cw-font-heading);
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
    }

    /* Search Results Header */
    .search-results-header {
        margin: 2rem 0;
        padding: 1.5rem 0;
        border-bottom: 1px solid var(--cw-neutral-200);
    }

    .results-count {
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 1.125rem;
        font-family: var(--cw-font-heading);
    }

    /* Custom Cards */
    .card-cw {
        border: 1px solid var(--cw-neutral-200);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
        background: white;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .card-cw:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-lg);
    }

    .venue-card {
        cursor: pointer;
    }

    .card-img-container {
        position: relative;
        overflow: hidden;
        border-radius: 1rem 1rem 0 0;
    }

    .card-img-top {
        transition: transform 0.3s ease;
        height: 200px;
        object-fit: cover;
    }

    .venue-card:hover .card-img-top {
        transform: scale(1.05);
    }

    /* Card Body */
    .card-body {
        padding: 1.5rem;
    }

    .card-title {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.75rem;
        font-size: 1.25rem;
    }

    /* Rating and Location */
    .rating {
        margin-bottom: 0.75rem;
    }

    .rating-score {
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 1rem;
    }

    .review-count {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
        margin-left: 0.5rem;
    }

    .location {
        color: var(--cw-neutral-600);
        font-weight: 500;
        margin-bottom: 0.75rem;
        font-size: 0.875rem;
    }

    /* Services Section */
    .services-section {
        border-top: 1px solid var(--cw-neutral-200);
        padding-top: 1rem;
        margin-top: 1rem;
    }

    .services-title {
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 0.75rem;
        font-family: var(--cw-font-heading);
    }

    .service-item {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-neutral-200);
        border-radius: 0.5rem;
        transition: all 0.2s ease;
        margin-bottom: 0.5rem;
    }

    .service-item:hover {
        background: var(--cw-brand-accent);
        border-color: var(--cw-brand-primary);
    }

    .service-name {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 0.25rem;
        font-family: var(--cw-font-heading);
    }

    .service-description {
        font-size: 0.75rem;
        line-height: 1.3;
        color: var(--cw-neutral-600);
    }

    .service-price {
        font-size: 0.875rem;
        color: var(--cw-brand-primary);
        font-weight: 600;
    }

    .service-duration {
        font-size: 0.75rem;
        color: var(--cw-neutral-600);
    }

    .add-to-cart-btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        white-space: nowrap;
        border: 1px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        background: white;
        transition: all 0.2s ease;
    }

    .add-to-cart-btn:hover {
        background: var(--cw-brand-primary);
        color: white;
    }

    .more-services {
        font-style: italic;
        color: var(--cw-neutral-600);
        font-size: 0.75rem;
    }

    /* Badges */
    .badge-cw-primary {
        background: var(--cw-brand-primary);
        color: white;
        font-weight: 500;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
    }

    .badge-cw-secondary {
        background: var(--cw-neutral-100);
        color: var(--cw-neutral-700);
        font-weight: 500;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
    }

    /* Pagination */
    .pagination .page-link {
        color: var(--cw-brand-primary);
        border: 1px solid var(--cw-neutral-200);
        background: white;
        margin: 0 0.25rem;
        border-radius: 0.5rem;
        padding: 0.5rem 0.75rem;
        transition: all 0.2s ease;
    }

    .pagination .page-link:hover {
        background: var(--cw-brand-primary);
        color: white;
        border-color: var(--cw-brand-primary);
    }

    .pagination .page-item.active .page-link {
        background: var(--cw-brand-primary);
        color: white;
        border-color: var(--cw-brand-primary);
    }

    /* No Results */
    .no-results {
        text-align: center;
        padding: 4rem 2rem;
        background: white;
        border: 1px solid var(--cw-neutral-200);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-md);
    }

    .no-results i {
        font-size: 4rem;
        color: var(--cw-brand-primary);
        opacity: 0.7;
        margin-bottom: 2rem;
    }

    .no-results h3 {
        font-family: var(--cw-font-heading);
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
    }

    .no-results p {
        color: var(--cw-neutral-600);
        margin-bottom: 2rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .page-title {
            font-size: 2.25rem;
        }

        .search-filters {
            padding: 1.5rem;
        }

        .filter-section {
            margin-bottom: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="search-section">
    <div class="search-container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">{% trans "Search Venues" %}</h1>
            <p class="page-subtitle">{% trans "Find the perfect spa and wellness venue for you" %}</p>
        </div>

        <!-- Search and Filter Form -->
        <div class="search-filters">
            <form method="get" action="{% url 'venues_app:venue_search' %}">
                <div class="row">
                    <!-- Search Query -->
                    <div class="col-md-4 filter-section">
                        <label class="filter-label">{% trans "Search" %}</label>
                        <input type="text" name="query" class="form-control form-control-cw"
                               value="{{ search_form.query.value|default:'' }}"
                               placeholder="{% trans 'Search venues, services, or categories...' %}">
                    </div>

                    <!-- Location -->
                    <div class="col-md-4 filter-section">
                        <label class="filter-label">{% trans "Location" %}</label>
                        <input type="text" name="location" class="form-control form-control-cw"
                               value="{{ search_form.location.value|default:'' }}"
                               placeholder="{% trans 'City, State' %}">
                    </div>

                    <!-- Category -->
                    <div class="col-md-4 filter-section">
                        <label class="filter-label">{% trans "Category" %}</label>
                        <select name="category" class="form-select form-select-cw">
                            <option value="">{% trans "All Categories" %}</option>
                            {% for choice in search_form.category.field.choices %}
                                {% if choice.0 %}
                                <option value="{{ choice.0 }}" {% if search_form.category.value == choice.0 %}selected{% endif %}>
                                    {{ choice.1 }}
                                </option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <div class="row">
                    <!-- Sort By -->
                    <div class="col-md-6 filter-section">
                        <label class="filter-label">{% trans "Sort By" %}</label>
                        <select name="sort_by" class="form-select form-select-cw">
                            {% for choice in filter_form.sort_by.field.choices %}
                            <option value="{{ choice.0 }}" {% if filter_form.sort_by.value == choice.0 %}selected{% endif %}>
                                {{ choice.1 }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12 d-flex flex-column flex-sm-row gap-3">
                        <button type="submit" class="btn btn-cw-primary" aria-label="{% trans 'Search venues' %}">
                            <i class="fas fa-search me-2"></i>{% trans "Search" %}
                        </button>
                        <a href="{% url 'venues_app:venue_search' %}" class="btn btn-cw-secondary">
                            <i class="fas fa-times me-2"></i>{% trans "Clear Filters" %}
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Search Results Header -->
        <div class="search-results-header">
            <div class="results-count">
                {% if is_search_results %}
                    {{ total_venues }} venue{{ total_venues|pluralize }} found
                {% else %}
                    Showing all {{ total_venues }} venue{{ total_venues|pluralize }}
                {% endif %}
            </div>
        </div>

        <!-- Venue Results -->
        {% if page_obj %}
        <div class="row g-4">
            {% for venue in page_obj %}
            <div class="col-lg-4 col-md-6">
                <div class="card-cw h-100 venue-card" data-venue-url="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">
                    <div class="card-img-container">
                        <img src="{{ venue.get_primary_image|default:'https://via.placeholder.com/317x177' }}"
                             class="card-img-top" alt="{{ venue.venue_name }}">
                        {% venue_badge_card venue %}
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">{{ venue.venue_name }}</h5>
                        <div class="rating">
                            <span class="rating-score">{{ venue.avg_rating|default:"New" }}{% if venue.avg_rating %}★{% endif %}</span>
                            <span class="review-count">({{ venue.review_count|default:"0" }})</span>
                        </div>
                        <p class="location">
                            <i class="fas fa-map-marker-alt me-1"></i>{{ venue.city }}, {{ venue.state }}
                        </p>
                        <p class="card-text text-muted">{{ venue.short_description|truncatewords:15 }}</p>

                        <!-- Categories -->
                        {% if venue.categories.all %}
                        <div class="categories mb-3">
                            {% for category in venue.categories.all %}
                                <span class="badge-cw-secondary me-1">{{ category.name }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}

                        <!-- Services Section -->
                        {% if venue.services.all %}
                        <div class="services-section mb-3">
                            <h6 class="services-title mb-2">Featured Services</h6>
                            <div class="services-list">
                                {% for service in venue.services.all|slice:":3" %}
                                <div class="service-item mb-2 p-2 border rounded">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="service-details flex-grow-1">
                                            <h6 class="service-name mb-1">{{ service.service_title }}</h6>
                                            <p class="service-description mb-1 text-muted small">
                                                {{ service.short_description|truncatewords:8 }}
                                            </p>
                                            <div class="service-meta d-flex align-items-center">
                                                <span class="service-price fw-bold me-2">{{ service.price_display }}</span>
                                                <span class="service-duration text-muted small">{{ service.duration_display }}</span>
                                            </div>
                                        </div>
                                        {% if user.is_authenticated and user.is_customer %}
                                        <div class="service-actions ms-2">
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-primary add-to-cart-btn"
                                                    data-service-id="{{ service.id }}"
                                                    data-service-name="{{ service.service_title }}"
                                                    data-venue-name="{{ venue.venue_name }}"
                                                    onclick="event.stopPropagation(); showAddToCartModal({{ service.id }}, '{{ service.service_title }}', '{{ venue.venue_name }}');">
                                                <i class="fas fa-cart-plus me-1"></i>Add
                                            </button>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endfor %}

                                {% if venue.services.all.count > 3 %}
                                <div class="more-services text-center mt-2">
                                    <small class="text-muted">+{{ venue.services.all.count|add:"-3" }} more service{{ venue.services.all.count|add:"-3"|pluralize }}</small>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% else %}
                        <div class="no-services mb-3">
                            <small class="text-muted">No services available</small>
                        </div>
                        {% endif %}

                        <!-- View Details Button -->
                        <div class="text-center mt-3">
                            <span class="btn btn-cw-secondary btn-sm">
                                <i class="fas fa-eye me-1"></i>{% trans "View Details" %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="Search results pagination" class="mt-5">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <!-- No Results -->
        <div class="no-results">
            <i class="fas fa-search"></i>
            <h3>{% trans "No venues found" %}</h3>
            <p>{% trans "Try adjusting your search criteria or browse all venues." %}</p>
            <a href="{% url 'venues_app:venue_search' %}" class="btn btn-cw-primary" aria-label="Browse all venues">
                <i class="fas fa-list me-2"></i>{% trans "Browse All Venues" %}
            </a>
        </div>
        {% endif %}
    </div>
</section>

<!-- Add to Cart Modal -->
{% if user.is_authenticated and user.is_customer %}
<div class="modal fade" id="addToCartModal" tabindex="-1" aria-labelledby="addToCartModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" style="border: none; border-radius: 1rem; box-shadow: var(--cw-shadow-xl);">
            <div class="modal-header border-0">
                <h5 class="modal-title fw-bold" id="addToCartModalLabel" style="color: var(--cw-brand-primary); font-family: var(--cw-font-heading);">
                    {% trans "Add Service to Cart" %}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="modalServiceInfo" class="mb-4 p-3 rounded" style="background: var(--cw-accent-light);">
                    <h6 id="modalServiceName" style="color: var(--cw-brand-primary); font-family: var(--cw-font-heading); font-weight: 600;"></h6>
                    <p class="mb-0" id="modalVenueName" style="color: var(--cw-neutral-600);"></p>
                </div>
                <form id="addToCartForm" method="post">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="selected_date" class="form-label fw-bold" style="color: var(--cw-brand-primary);">{% trans "Select Date" %}</label>
                        <input type="date" class="form-control form-control-cw" id="selected_date" name="selected_date" required>
                    </div>
                    <div class="mb-3">
                        <label for="selected_time_slot" class="form-label fw-bold" style="color: var(--cw-brand-primary);">{% trans "Select Time" %}</label>
                        <select class="form-select form-select-cw" id="selected_time_slot" name="selected_time_slot" required>
                            <option value="">{% trans "Choose a time slot..." %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="quantity" class="form-label fw-bold" style="color: var(--cw-brand-primary);">{% trans "Quantity" %}</label>
                        <input type="number" class="form-control form-control-cw" id="quantity" name="quantity" min="1" max="10" value="1" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-cw-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-cw-primary" id="confirmAddToCart">{% trans "Add to Cart" %}</button>
            </div>
        </div>
    </div>
</div>
{% endif %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Make venue cards clickable
    const venueCards = document.querySelectorAll('.venue-card');
    venueCards.forEach(card => {
        card.addEventListener('click', function(e) {
            // Don't navigate if clicking on add to cart button
            if (e.target.closest('.add-to-cart-btn')) {
                return;
            }

            const venueUrl = this.dataset.venueUrl;
            if (venueUrl) {
                window.location.href = venueUrl;
            }
        });
    });

    {% if user.is_authenticated and user.is_customer %}
    // Add to cart functionality
    let currentServiceId = null;
    const addToCartModal = new bootstrap.Modal(document.getElementById('addToCartModal'));

    // Set minimum date to tomorrow
    const dateInput = document.getElementById('selected_date');
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    dateInput.min = tomorrow.toISOString().split('T')[0];

    // Set maximum date to 30 days from now
    const maxDate = new Date();
    maxDate.setDate(maxDate.getDate() + 30);
    dateInput.max = maxDate.toISOString().split('T')[0];

    // Show add to cart modal
    window.showAddToCartModal = function(serviceId, serviceName, venueName) {
        currentServiceId = serviceId;
        document.getElementById('modalServiceName').textContent = serviceName;
        document.getElementById('modalVenueName').textContent = venueName;

        // Reset form
        document.getElementById('addToCartForm').reset();
        document.getElementById('selected_time_slot').innerHTML = '<option value="">Choose a time slot...</option>';

        addToCartModal.show();
    };

    // Load time slots when date changes
    dateInput.addEventListener('change', function() {
        const selectedDate = this.value;
        const timeSlotSelect = document.getElementById('selected_time_slot');

        if (selectedDate && currentServiceId) {
            // Clear existing options
            timeSlotSelect.innerHTML = '<option value="">Loading...</option>';

            // Fetch available time slots
            fetch(`/bookings/ajax/slots/${currentServiceId}/?date=${selectedDate}`)
                .then(response => response.json())
                .then(data => {
                    timeSlotSelect.innerHTML = '<option value="">Choose a time slot...</option>';

                    if (data.slots && data.slots.length > 0) {
                        data.slots.forEach(slot => {
                            const option = document.createElement('option');
                            option.value = slot.time;
                            option.textContent = `${slot.display} (${slot.available_spots} available)`;
                            timeSlotSelect.appendChild(option);
                        });
                    } else {
                        timeSlotSelect.innerHTML = '<option value="">No available time slots</option>';
                    }
                })
                .catch(error => {
                    console.error('Error loading time slots:', error);
                    timeSlotSelect.innerHTML = '<option value="">Error loading time slots</option>';
                });
        }
    });

    // Handle add to cart confirmation
    document.getElementById('confirmAddToCart').addEventListener('click', function() {
        const form = document.getElementById('addToCartForm');
        const formData = new FormData(form);

        if (currentServiceId && form.checkValidity()) {
            // Submit form to add to cart
            fetch(`/bookings/cart/add/${currentServiceId}/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            })
            .then(response => {
                if (response.ok) {
                    addToCartModal.hide();
                    // Show success message
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
                    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
                    alertDiv.innerHTML = `
                        <strong>Success!</strong> Service added to cart.
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.body.appendChild(alertDiv);

                    // Auto-remove alert after 3 seconds
                    setTimeout(() => {
                        if (alertDiv.parentNode) {
                            alertDiv.remove();
                        }
                    }, 3000);
                } else {
                    throw new Error('Failed to add to cart');
                }
            })
            .catch(error => {
                console.error('Error adding to cart:', error);
                // Show error message
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
                alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
                alertDiv.innerHTML = `
                    <strong>Error!</strong> Failed to add service to cart. Please try again.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.body.appendChild(alertDiv);

                // Auto-remove alert after 5 seconds
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            });
        } else {
            // Show validation errors
            form.reportValidity();
        }
    });
    {% endif %}
});
</script>
{% endblock %}
